using Domain.Enums;
using Domain.Messages.Commands.Notification;
using Elsa.Activities.ControlFlow;
using Elsa.Activities.MassTransit;
using Elsa.Activities.Primitives;
using Elsa.Builders;
using Workflow.Notification.AddNotification.Activities;
using static Workflow.Constants.NotificationWorkFlow;

namespace Workflow.Notification.AddNotification
{
    public class AddNotificationWorkflow : IWorkflow
    {
        public void Build(IWorkflowBuilder builder)
        {
            builder
                .WithDisplayName("Process new Notification")
                .ReceiveMassTransitMessage(
                    activity => activity.Set(x => x.MessageType, x => typeof(CreateNotification)))
                .WithDisplayName("Receive new notification message")
                .WithDescription("When receive the message, a new notification will be created.")
                .SetTransientVariable(Variables.Message, context => context.GetInput<CreateNotification>())
                .Switch(cases => cases
                    .Add(context => context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.StructureUpdated
                                    || context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.StructureDeleted,
                        @case => @case
                            .Then<GenerateStructurePayloadActivity>()
                            .WithDisplayName("Process Structure Notification")
                            .WithDescription("This activity will process a structure notification")
                    )
                    .Add(
                        context => context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.InstrumentCreated
                        || context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.InstrumentUpdated
                        || context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.InstrumentDeleted,
                        @case => @case
                            .Then<GenerateInstrumentationPayloadActivity>()
                            .WithDisplayName("Process Instrumentation Notification")
                            .WithDescription("This activity will process an instrumentation notification")
                    )
                    .Add(
                        context => context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.ReadingCreated
                        || context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.ReadingUpdated
                        || context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.ReadingDeleted,
                        @case => @case
                            .Then<GenerateReadingsPayloadActivity>()
                            .WithDisplayName("Process Readings Notification")
                            .WithDescription("This activity will process a readings notification")
                    )
                    .Add(
                        context => context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.ControlLetterNewRegistry,
                        @case => @case
                            .Then<GenerateControlLetterPayloadActivity>()
                            .WithDisplayName("Process Control Letter Notification")
                            .WithDescription("This activity will process a Control Letter notification")
                    )
                    .Add(
                        context => context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.InstrumentRepaired
                        || context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.InstrumentDamaged,
                        @case => @case
                            .Then<GenerateRepairedOrDamagedInstrumentPayloadActivity>()
                            .WithDisplayName("Process Instrumet Repaired or Damaged Notification")
                            .WithDescription("This activity will process Instrumet Repaired or Damaged notification")
                    )
                    .Add(
                        context => context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.StabilityAnalysisCreated
                        || context.GetTransientVariable<CreateNotification>(Variables.Message).Theme == NotificationTheme.StabilityAnalysisUpdated,
                        @case => @case
                            .Then<GenerateStabilityAnalysisPayloadActivity>()
                    )
                )
                .Then<SendNotificationActivity>()
                .WithDisplayName("Create a new notification")
                .WithDescription("This activity will create a notification")
                .WithName("Finish");
        }
    }
}