using Application.Apis.Clients.Response.Report;
using Application.Report;
using CronExpressionDescriptor;
using Elsa;
using Elsa.Activities.ControlFlow;
using Elsa.Activities.Primitives;
using Elsa.Activities.Temporal;
using Elsa.Activities.Workflows;
using Elsa.Builders;
using Microsoft.Extensions.Options;
using Workflow.Report.Activities;
using Workflow.Report.GenerateReport;
using static Elsa.Activities.Workflows.RunWorkflow;
using static Workflow.Constants.ReportWorkFlow;

namespace Workflow.Report.FetchPendingReports
{
    public class FetchPendingReportsWorkflow : IWorkflow
    {
        private readonly ScheduledReportOptions _reportOptions;

        public FetchPendingReportsWorkflow(
            IOptions<ScheduledReportOptions> reportOptions)
        {
            _reportOptions = reportOptions.Value;
        }

        public void Build(IWorkflowBuilder builder)
        {
            var readableCronExpression = ExpressionDescriptor
                .GetDescription(_reportOptions.CronExpression);
            
            builder
                .WithDeleteCompletedInstances(true)
                .AsSingleton()
                .WithDisplayName("Fetch Pending Reports")
                .Cron(_reportOptions.CronExpression)
                .WithDisplayName($"Runs {readableCronExpression}")
                .SetVariable(value: _reportOptions.CronExpression, name: Variables.CronExpression)
                .SetVariable(value: _reportOptions.HoursToLookBack, name: Variables.HoursToLookBack)
                .Then<FetchPendingReportsActivity>(activityBuilder =>
                {
                    activityBuilder
                        .When(OutcomeNames.False)
                        .Finish();

                    activityBuilder
                        .When(OutcomeNames.True)
                        .ForEach(items: context => context.GetTransientVariable<List<GetPendingReportResponse>>(Variables.PendingReports)!,
                            iterate: item =>
                            {
                                item.RunWorkflow<GenerateReportWorkflow>(
                                    mode: RunWorkflowMode.FireAndForget,
                                    input: context => context.GetVariable<GetPendingReportResponse>(Variables.CurrentValue)
                                );
                            }
                        )
                        .When(OutcomeNames.Done);
                });
        }
    }
}
