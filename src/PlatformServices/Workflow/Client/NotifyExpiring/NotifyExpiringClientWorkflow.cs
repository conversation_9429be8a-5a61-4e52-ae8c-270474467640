using Application.Apis.Clients.Response;
using Application.Apis.Users.Response;
using Elsa;
using Elsa.Activities.ControlFlow;
using Elsa.Activities.Temporal;
using Elsa.Builders;
using Workflow.Client.NotifyExpiring.Activities;
using static Workflow.Constants.ClientWorkFlow;

namespace Workflow.Client.NotifyExpiring
{
    public class NotifyExpiringClientWorkflow : IWorkflow
    {
        public void Build(IWorkflowBuilder builder)
        {
            builder
                .AsSingleton()
                .WithDisplayName("Notify users related to expiring clients workflow")
                .Cron("0 0 12 * * ?")
                .WithDisplayName("Cron running every day at 12:00UTC")
                .Then<GetExpiringClientsActivity>()
                .WithDisplayName("Get expiring clients")
                .If(c => c.GetTransientVariable<List<GetExpiringClientsResponse>>(Variables.ExpiringClients) != null,
                    @if =>
                    {
                        @if
                            .When(OutcomeNames.True)
                            .ForEach(context => context.GetTransientVariable<List<GetExpiringClientsResponse>>(Variables.ExpiringClients)!,
                            item =>
                            {
                                item
                                   .Then<GetUsersToNotifyActivity>()
                                   .WithDisplayName("Get users to notify")
                                   .If(c =>
                                   {
                                       var usersToNotify = c
                                        .GetTransientVariable<IEnumerable<GetUserEmailResponse>>(Variables.UsersToNotify);

                                       return usersToNotify == null || !usersToNotify.Any();
                                   }, @if =>
                                   {
                                       @if
                                        .When(OutcomeNames.False)
                                        .Then<AddNotificationToExpiringClientsActivity>()
                                        .WithDisplayName("Add notification to users related expiring clients")
                                        .Then<SendEmailToExpiringClientsActivity>()
                                        .WithDisplayName("Send email to users related to expiring clients");
                                   });
                            });
                    });
        }
    }
}
