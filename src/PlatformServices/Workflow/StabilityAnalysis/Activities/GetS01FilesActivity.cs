using Application.Apis.Slide2;
using Application.Apis.Slide2.Response;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using System.Text;
using Workflow.StabilityAnalysis.Classes;
using static Workflow.Constants.StabilityAnalysisWorkFlow;

namespace Workflow.StabilityAnalysis.Activities
{
    public sealed class GetS01FilesActivity : Activity
    {
        private readonly ISlide2ApiService _slide2ApiService;
        private readonly int _numberOfRetries = 15;
        private readonly int _delayBetweenRetries = 60000;

        public GetS01FilesActivity(ISlide2ApiService slide2ApiService)
        {
            _slide2ApiService = slide2ApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sliFiles = context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

                foreach (var sliFile in sliFiles)
                {
                    var fileId = await _slide2ApiService.CreateS01File(new()
                    {
                        SliBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sliFile.SliFile.Save())),
                        SltmBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sliFile.SltmFile.Save()))
                    });

                    if (fileId == Guid.Empty)
                    {
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não conseguimos continuar com a análise de estabilidade da seção {sliFile.Section.SectionName} porque o sistema que realiza o cálculo não forneceu o arquivo de resultado esperado.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var s01File = new GetS01FileResponse();

                    for (int i = 0; i < _numberOfRetries; i++)
                    {
                        await Task.Delay(_delayBetweenRetries);

                        s01File = await _slide2ApiService.GetS01File(fileId);

                        if (s01File != null)
                        {
                            break;
                        }
                    }

                    if (s01File == null || string.IsNullOrEmpty(s01File.S01Base64))
                    {
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não conseguimos continuar com a análise de estabilidade da seção {sliFile.Section.SectionName} porque o sistema que realiza o cálculo não forneceu o arquivo de resultado esperado.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var bytes = Convert.FromBase64String(s01File.S01Base64);
                    var text = Encoding.UTF8.GetString(bytes);

                    sliFile.S01 = text;
                }

                return Done();

            }
            catch (Exception e)
            {
                Log.Error(e, "Error in GetS01FilesActivity");
                context.SetTransientVariable(Variables.ErrorMessage, "Não conseguimos continuar com a análise de estabilidade porque houve um erro ao tentar obter o arquivo de resultado necessário.");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
