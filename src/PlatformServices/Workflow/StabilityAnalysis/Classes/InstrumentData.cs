using Domain.Enums;

namespace Workflow.StabilityAnalysis.Classes
{
    public sealed class InstrumentData
    {
        public Guid InstrumentId { get; set; }
        public Guid? MeasurementId { get; set; }
        public InstrumentType InstrumentType { get; set; }
        public LinimetricRulerPosition? LinimetricRulerPosition { get; set; }
        public DryType? InstrumentDryType { get; set; }
        public decimal? ReadingQuota { get; set; }
        public bool DryReading { get; set; }
    }
}
