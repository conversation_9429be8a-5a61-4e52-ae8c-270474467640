using Elsa.Activities.ControlFlow;
using Elsa.Activities.Temporal;
using Elsa.Builders;

namespace Workflow.Simulation.DeleteOld
{
    public sealed class DeleteOldSimulationsWorkflow : IWorkflow
    {
        public void Build(IWorkflowBuilder builder)
        {
            builder
               .WithDisplayName("Delete old simulations workflow")
               .Cron("0 0 5 * * ?")
               .WithDisplayName("Cron running every day at 05:00UTC")
               .Then<DeleteOldSimulationsActivity>()
               .Finish();
        }
    }
}
