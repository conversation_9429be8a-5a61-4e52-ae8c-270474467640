using Application.Apis.Clients;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.SimulationWorkFlow;

namespace Workflow.Simulation.StartSimulation.Activities
{
    public class GetSimulationActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GetSimulationActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var simulationId = context.GetTransientVariable<Domain.Messages.Commands.Simulation.StartSimulation>(Variables.Command).Id;

                var simulation = await _clientsApiService
                    .GetSimulationById(simulationId);

                if (simulation == null)
                {
                    Log.Error($"Simulation with id {simulationId} not found");
                    return Outcome(OutcomeNames.Cancel);
                }

                context.SetTransientVariable(Variables.Simulation, simulation);

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in GetSimulationActivity");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
