using Application.Apis.Clients;
using Application.Apis.Clients.Request.Simulation.Patch;
using Application.Apis.Clients.Response.Simulation.GetById;
using Domain.Enums;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Serilog;
using System.Text;
using Workflow.StabilityAnalysis.Classes;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;
using SimulationVariables = Workflow.Constants.SimulationWorkFlow.Variables;

namespace Workflow.Simulation.StartSimulation.Activities
{
    public class PatchSimulationActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public PatchSimulationActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var simulation = context.GetTransientVariable<GetSimulationByIdResponse>(SimulationVariables.Simulation);
                var sliFiles = context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);
                var groupedSections = sliFiles.GroupBy(x => x.Section.SectionId).ToList();

                var payload = new PatchSimulationRequest()
                {
                    Id = simulation.Id,
                    Status = SimulationStatus.Completed,
                    DownstreamLinimetricRulerQuota = simulation.DownstreamLinimetricRulerQuota,
                    UpstreamLinimetricRulerQuota = simulation.UpstreamLinimetricRulerQuota,
                    Sections = simulation.Sections.Select(x => new PatchSimulationSection()
                    {
                        Id = x.Id,
                        BeachLength = x.BeachLength,
                        IgnoredInstruments = x.IgnoredInstruments,
                        SectionId = x.SectionId,
                        Instruments = x.Instruments.Select(y => new PatchSimulationInstrument()
                        {
                            Id = y.Id,
                            InstrumentId = y.InstrumentId,
                            MeasurementId = y.MeasurementId,
                            Quota = (decimal)y.Quota
                        }).ToList(),
                        Results = new()
                    }).ToList()
                };

                foreach (var groupSection in groupedSections)
                {
                    var files = groupSection.ToList();

                    var sectionPayload = payload.Sections.First(x => x.SectionId == groupSection.Key);

                    foreach (var file in files)
                    {
                        var result = new PatchSimulationResult()
                        {
                            SliFile = new()
                            {
                                Base64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(file.SliFile.Save())),
                                Name = "sli_used.sli"
                            },
                            SltmFile = new()
                            {
                                Base64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(file.SltmFile.Save())),
                                Name = "sltm_used.sltm"
                            },
                            SliFileType = file.SliFileType,
                            Values = new()
                        };

                        foreach (var fs in file.Values)
                        {
                            result.Values.Add(new()
                            {
                                CalculationMethod = fs.CalculationMethod,
                                DxfFile = new()
                                {
                                    Base64 = fs.DxfBase64,
                                    Name = "analysis_result_dxf_file.dxf"
                                },
                                PngFile = new()
                                {
                                    Base64 = fs.PngBase64,
                                    Name = "analysis_result_png_file.png"
                                },
                                Value = fs.Value
                            });
                        }

                        sectionPayload.Warnings = file.Warnings;
                        sectionPayload.Results.Add(result);
                    }
                }

                var response = await _clientsApiService.PatchSimulation(payload);

                if (response == null || !response.IsSuccessStatusCode)
                {
                    context.SetTransientVariable(Variables.ErrorMessage, "Ocorreu um erro ao tentar atualizar a simulação.");
                    return Outcome(OutcomeNames.Cancel);
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in PatchSimulationActivity");
                context.SetTransientVariable(Variables.ErrorMessage, "Ocorreu um erro ao tentar atualizar a simulação.");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
