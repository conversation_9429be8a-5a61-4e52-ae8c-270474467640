using Azure.Monitor.OpenTelemetry.AspNetCore;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;

namespace Api.Configuration
{
    public static class TelemetryCfg
    {
        private const string SERVICE_NAME = "workflows-api";
        private const string SERVICE_VERSION = "v1";

        public static IServiceCollection AddTelemetry(
            this IServiceCollection services,
            AzureMonitorOptions azureMonitorOptions)
        {
            services
                .AddOpenTelemetry()
                .UseAzureMonitor(options =>
                {
                    options.ConnectionString = azureMonitorOptions.ConnectionString;
                    options.SamplingRatio = azureMonitorOptions.SamplingRatio;
                    options.EnableLiveMetrics = azureMonitorOptions.EnableLiveMetrics;
                    options.DisableOfflineStorage = azureMonitorOptions.DisableOfflineStorage;
                })
                .ConfigureResource(resourceBuilder =>
                    resourceBuilder.AddService(
                        serviceName: SERVICE_NAME,
                        serviceVersion: SERVICE_VERSION)
                )
                .WithTracing(traceBuilder =>
                {
                    traceBuilder
                        .SetSampler(new AlwaysOnSampler())
                        .AddAspNetCoreInstrumentation(options => options.Filter = GetExcludedRequests)
                        .AddOtlpExporter();
                })
                .WithMetrics()
                .WithLogging(loggingBuilder =>
                {
                    loggingBuilder
                        .AddOtlpExporter();
                });

            return services;
        }

        private static readonly Func<HttpContext, bool> GetExcludedRequests = (httpContext) =>
        {
            return !(httpContext.Request.Path.Value.EndsWith(".css") ||
                httpContext.Request.Path.Value.EndsWith(".js") ||
                httpContext.Request.Path.Value.EndsWith(".html") ||
                httpContext.Request.Path.Value.EndsWith(".ico") ||
                httpContext.Request.Method.Equals("OPTIONS") ||
                httpContext.Request.Method.Equals("HEAD")) ||
                httpContext.Request.Path.Value.Equals("/health");
        };
    }
}