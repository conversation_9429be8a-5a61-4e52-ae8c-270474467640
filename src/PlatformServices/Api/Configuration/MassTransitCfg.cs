using Domain.Messages.Commands.InspectionSheet;
using Domain.Messages.Commands.Notification;
using Domain.Messages.Commands.Package;
using Domain.Messages.Commands.Section;
using Domain.Messages.Commands.Simulation;
using Domain.Messages.Events.Client;
using Domain.Messages.Events.OccurrenceListReport;
using Domain.Messages.Events.Reading;
using Domain.Messages.Events.User;
using Elsa.Activities.MassTransit.Consumers;
using MassTransit;

namespace Api.Configuration
{
    public static class MassTransitCfg
    {
        public static IServiceCollection AddMassTransit(
            this IServiceCollection services,
            string connectionString)
        {
            services
                 .AddMassTransit(x =>
                 {
                     x.AddWorkflowConsumers();

                     x.SetKebabCaseEndpointNameFormatter();

                     x.UsingRabbitMq((ctx, cfg) =>
                     {
                         cfg.Host(connectionString);

                         cfg.ConfigureEndpoints(ctx);
                     });
                 });

            return services;
        }

        private static IBusRegistrationConfigurator AddWorkflowConsumers(
            this IBusRegistrationConfigurator configurator)
        {
            static Type CreateWorkflowConsumer(Type messageType) =>
                typeof(WorkflowConsumer<>).MakeGenericType(messageType);

            configurator.AddConsumer(CreateWorkflowConsumer(typeof(DisabledClient)));
            configurator.AddConsumer(CreateWorkflowConsumer(typeof(AcceptedTerm)));
            configurator.AddConsumer(CreateWorkflowConsumer(typeof(ReadingCreated)));
            configurator.AddConsumer(CreateWorkflowConsumer(typeof(ReadingUpdated)));
            configurator.AddConsumer(CreateWorkflowConsumer(typeof(CalculateStability)));
            configurator.AddConsumer(CreateWorkflowConsumer(typeof(CreateSliFile)));
            configurator.AddConsumer(CreateWorkflowConsumer(typeof(StartSimulation)));
            configurator.AddConsumer(CreateWorkflowConsumer(typeof(CreateNotification)));
            configurator.AddConsumer(CreateWorkflowConsumer(typeof(OccurrenceListReportCreated)));
            configurator.AddConsumer(CreateWorkflowConsumer(typeof(TranscribeInspectionSheetVoiceNotes)));

            return configurator;
        }
    }
}
