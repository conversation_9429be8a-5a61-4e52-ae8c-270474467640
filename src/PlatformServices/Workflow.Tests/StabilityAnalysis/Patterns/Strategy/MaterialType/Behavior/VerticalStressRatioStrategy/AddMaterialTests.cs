using Bogus;
using FluentAssertions;
using Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType.Behavior;
using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Slide.Core;
using Slide.Core.Objects.Sli;
using Xunit;

namespace Workflow.Tests.StabilityAnalysis.Patterns.Strategy.MaterialType.Behavior.VerticalStressRatioStrategy;

[Trait("VerticalStressRatioStrategy", "AddMaterial")]
public class AddMaterialTests
{
    private readonly Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType.Behavior.VerticalStressRatioStrategy _strategy = new();

    [Fact]
    public void AddMaterial_ShouldAddMaterialToSliFile()
    {
        // Arrange
        var sliFile = new SliFile();
        
        var materialValue = new Faker<GetStaticMaterialValueBySearchId>()
            .RuleFor(m => m.Id, f => Guid.NewGuid())
            .RuleFor(m => m.Color, f => f.Internet.Color())
            .RuleFor(m => m.NaturalSpecificWeight, f => f.Random.Double(10, 20))
            .RuleFor(m => m.SaturatedSpecificWeight, f => f.Random.Double(20, 30))
            .RuleFor(m => m.CustomHuValue, f => f.Random.Double(0, 1))
            .RuleFor(m => m.WaterSurface, f => f.PickRandom<Domain.Enums.WaterSurface>())
            .RuleFor(m => m.Hu, f => f.PickRandom<Domain.Enums.Hu>())
            .RuleFor(m => m.UseDrainedResistanceOverWaterSurface, f => f.Random.Bool())
            .RuleFor(m => m.PointValues, f => new List<PointValue>
            {
                new PointValue { Value1 = f.Random.Double(0, 10), Value2 = f.Random.Double(0, 10) }
            })
            .Generate();

        var materialResponse = new Faker<GetStaticMaterialBySearchIdResponse>()
            .RuleFor(m => m.Id, f => Guid.NewGuid())
            .RuleFor(m => m.SearchIdentifier, f => f.Random.Int(1, 100))
            .RuleFor(m => m.Name, f => f.Commerce.ProductName())
            .Generate();

        // Act
        _strategy.AddMaterial(sliFile, materialValue, materialResponse);

        // Assert
        sliFile.MaterialTypes.Should().HaveCount(1);
        sliFile.MaterialProperties.Should().HaveCount(1);

        var addedMaterial = sliFile.MaterialTypes.First();
        addedMaterial.MaterialValueId.Should().Be(materialValue.Id.Value);
        addedMaterial.MaterialSearchId.Should().Be(materialResponse.SearchIdentifier);
        addedMaterial.Type.Should().Be(Slide.Core.Enums.MaterialType.HoekBrown);

        var addedProperty = sliFile.MaterialProperties.First();
        addedProperty.Name.Should().Be(materialResponse.Name);
    }
}